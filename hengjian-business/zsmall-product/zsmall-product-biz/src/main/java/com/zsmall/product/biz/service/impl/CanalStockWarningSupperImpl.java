package com.zsmall.product.biz.service.impl;

import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.redis.utils.RedisUtils;
import com.rabbitmq.client.Channel;
import com.zsmall.product.biz.service.CanalStockWarningSupper;
import com.zsmall.product.biz.service.StockQueryService;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.domain.mq.CanalProductStockData;
import com.zsmall.product.entity.domain.mq.CanalStockWarningMqDTO;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Canal库存预警业务处理器实现
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CanalStockWarningSupperImpl implements CanalStockWarningSupper {

    private final StockQueryService stockQueryService;
    @Resource
    private ProductSkuStockMapper skuStockMapper;

    /**
     * 处理Canal库存预警消息
     */
    @Override
    public void dealCanalStockWarningMessage(Message message, Channel channel, Boolean isJob) throws Exception {
        String messageContext = new String(message.getBody());

        if (ObjectUtil.isNull(messageContext) || ObjectUtil.isNull(channel) || StrUtil.isEmpty(messageContext)) {
            log.warn("Canal库存预警消息参数为空");
            return;
        }

        // 解析消息
        CanalStockWarningMqDTO canalStockWarningDTO = JSON.parseObject(messageContext, CanalStockWarningMqDTO.class);
        log.info("接收到Canal库存预警变更信息，来源：{}，表名：{}，操作类型：{}，消息内容：{}",
                isJob ? "xxl-job" : "MQ", canalStockWarningDTO.getTable(), canalStockWarningDTO.getType(), messageContext);

        // 消息去重处理
        int hash = HashUtil.apHash(messageContext);
        if (!isJob) {
            boolean existsObject = RedisUtils.isExistsObject(GlobalConstants.GLOBAL_REDIS_KEY + hash);
            if (existsObject) {
                log.error("Canal库存预警消息HASH值已存在：{}", hash);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
                return;
            }
        } else {
            RedisUtils.setCacheObject(GlobalConstants.GLOBAL_REDIS_KEY + hash, hash);
            RedisUtils.expire(GlobalConstants.GLOBAL_REDIS_KEY + hash, 60 * 60);
        }

        // 处理不同类型的操作
        String table = canalStockWarningDTO.getTable();
        switch (table) {
            case "product":
                //获取商品SPU编码
                List<CanalProductStockData> data = canalStockWarningDTO.getData();
                break;
            case "product_sku_stock":
                //获取商品SKU编码
                List<CanalProductStockData> ps = canalStockWarningDTO.getData();
                //获取商品SKU编码转set
                Set<String> productSkuCodeSet=ps.stream().map(CanalProductStockData::getProductSkuCode).collect(Collectors.toSet());
                productSkuCodeSet.forEach(s->{
                    SkuStock stock=skuStockMapper.getDropShippingAllStock(s);
                    if(ObjectUtil.isNotNull(stock) && stock.getDropShippingStockTotal()!=0){
                        //表示有库存， 从缓存中获取当前库存状态
                        stockQueryService.getDropShippingStockQuantity(s,stock.getWarehouseSystemCode());
                    }
                });

                break;
            default:
                log.warn("未知的Canal数据库类型：{}", table);
        }
    }

    /**
     * 获取商品SKU的代发库存状态
     *
     * @param productSkuCode SKU编码
     * @return 代发库存数量，0表示无库存，>0表示有库存
     */
    public void getDropShippingAllStock(String productSkuCode){
        SkuStock stock=skuStockMapper.getDropShippingAllStock(productSkuCode);
    }




}
