package com.zsmall.product.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.redis.utils.RedisUtils;
import com.rabbitmq.client.Channel;
import com.zsmall.product.biz.service.CanalStockWarningSupper;
import com.zsmall.product.entity.domain.WarningMessage;
import com.zsmall.product.entity.domain.dto.productSku.SkuStockWarningContent;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.domain.mq.CanalProductStockData;
import com.zsmall.product.entity.domain.mq.CanalStockWarningMqDTO;
import com.zsmall.product.entity.mapper.ProductSkuStockMapper;
import com.zsmall.product.entity.mapper.WarningMessageMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Canal库存预警业务处理器实现
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CanalStockWarningSupperImpl implements CanalStockWarningSupper {
    @Resource
    private ProductSkuStockMapper skuStockMapper;
    @Resource
    private WarningMessageMapper warningMessageMapper;
    /**
     * 支持的物流方式-代发
     */
    private  final static Set<String> SUPPORTED_DROP_SHIPPING_LOGISTICS= Set.of("All", "DropShippingOnly");
    /**
     * 支持的物流方式-自提
     */
    private  final static Set<String> SUPPORTED_PICKUP_LOGISTICS= Set.of("All", "PickupOnly");
    /**
     * 处理Canal库存预警消息
     */
    @Override
    public void dealCanalStockWarningMessage(Message message, Channel channel, Boolean isJob) throws Exception {
        String messageContext = new String(message.getBody());

        if (ObjectUtil.isNull(messageContext) || ObjectUtil.isNull(channel) || StrUtil.isEmpty(messageContext)) {
            log.warn("Canal库存预警消息参数为空");
            return;
        }

        // 解析消息
        CanalStockWarningMqDTO canalStockWarningDTO = JSON.parseObject(messageContext, CanalStockWarningMqDTO.class);
        log.info("接收到Canal库存预警变更信息，来源：{}，表名：{}，操作类型：{}，消息内容：{}",
                isJob ? "xxl-job" : "MQ", canalStockWarningDTO.getTable(), canalStockWarningDTO.getType(), messageContext);

        // 消息去重处理
        int hash = HashUtil.apHash(messageContext);
        if (!isJob) {
            boolean existsObject = RedisUtils.isExistsObject(GlobalConstants.GLOBAL_REDIS_KEY + hash);
            if (existsObject) {
                log.error("Canal库存预警消息HASH值已存在：{}", hash);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
                return;
            }
        } else {
            RedisUtils.setCacheObject(GlobalConstants.GLOBAL_REDIS_KEY + hash, hash);
            RedisUtils.expire(GlobalConstants.GLOBAL_REDIS_KEY + hash, 60 * 60);
        }

        // 处理不同类型的操作
        String table = canalStockWarningDTO.getTable();
        switch (table) {
            case "product":
                //获取商品SPU编码
                List<CanalProductStockData> data = canalStockWarningDTO.getData();
                //获取商品SPU编码
                Set<String> productCodeList=data.stream().map(CanalProductStockData::getProductCode).collect(Collectors.toSet());
                productCodeList.forEach(s->{
                    //根据商品SPU编码获取商品SKU编码
                    List<SkuStock> skuStockList=skuStockMapper.getDropShippingAllStockBySpu(s,SUPPORTED_DROP_SHIPPING_LOGISTICS);
                    if (CollUtil.isEmpty(skuStockList)){
                        //代表这个spu下面的所有sku都没有代发库存，但是我们需要知道自提库存
                        List<SkuStock> pickList=skuStockMapper.getDropShippingAllStockBySpu(s,SUPPORTED_PICKUP_LOGISTICS);
                        pickList.forEach(ss->{
                            Boolean b = RedisUtils.hasKey(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS + ss.getProductSkuCode());
                            if (b){
                                Integer o = RedisUtils.getCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS + ss.getProductSkuCode());
                                if (ObjectUtil.isNotNull(o)){
                                    if (o!=0 && ss.getDropShippingStockTotal()==0){
                                        //表示从有库存变成无库存
                                        sendSkuStockWarningMessage(ss,2);
                                        RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+ss.getProductSkuCode(), 0);
                                    }
                                }
                            }
                        });

                    }else {
                        skuStockList.forEach(ss->{
                            Boolean b = RedisUtils.hasKey(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS + ss.getProductSkuCode());
                            if (!b){
                                //表示没有这个商品的代发库存标识(新增的)
                                //初始化库存标识
                                if ("INSERT".equals(canalStockWarningDTO.getType())){
                                    //表示新增的商品，需要初始化库存标识
                                    if (ss.getDropShippingStockTotal() == 0){
                                        RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+ss.getProductSkuCode(), 0);
                                    }else {
                                        RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+ss.getProductSkuCode(), 1);
                                    }
                                }
                            }else {
                                Integer o = RedisUtils.getCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS + ss.getProductSkuCode());
                                if (ObjectUtil.isNotNull(o)){
                                    if (o==0 && ss.getDropShippingStockTotal()!=0){
                                        //表示从无库存变成有库存
                                        sendSkuStockWarningMessage(ss,1);
                                        RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+ss.getProductSkuCode(), 1);
                                    }
                                    if (o!=0 && ss.getDropShippingStockTotal()==0){
                                        //表示从有库存变成无库存
                                        sendSkuStockWarningMessage(ss,2);
                                        RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+ss.getProductSkuCode(), 0);
                                    }
                                }
                            }

                        });
                    }


                });
                break;
            case "product_sku_stock":
                //获取商品SKU编码
                List<CanalProductStockData> ps = canalStockWarningDTO.getData();
                //获取商品SKU编码转set
                Set<String> productSkuCodeSet=ps.stream().map(CanalProductStockData::getProductSkuCode).collect(Collectors.toSet());
                productSkuCodeSet.forEach(s->{
                    SkuStock stock=skuStockMapper.getDropShippingAllStockBySku(s,SUPPORTED_DROP_SHIPPING_LOGISTICS);
                    Boolean b = RedisUtils.hasKey(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS + s);
                    if (!b){
                        //表示没有这个商品的代发库存标识(新增的)
                        //初始化库存标识
                        if ("INSERT".equals(canalStockWarningDTO.getType())){
                            //表示新增的商品，需要初始化库存标识
                            if (stock.getDropShippingStockTotal() == 0){
                                RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+s, 0);
                            }else {
                                RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+s, 1);
                            }
                        }
                    }else {
                        Integer o = RedisUtils.getCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS + s);
                        if (ObjectUtil.isNotNull(o)){
                            if (o==0 && stock.getDropShippingStockTotal()!=0){
                                //表示从无库存变成有库存
                                sendSkuStockWarningMessage(stock,1);
                                RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+s, 1);
                            }
                            if (o!=0 && stock.getDropShippingStockTotal()==0){
                                //表示从有库存变成无库存
                                sendSkuStockWarningMessage(stock,2);
                                RedisUtils.setCacheObject(GlobalConstants.SKU_DROP_SHIPPING_STOCK_STATUS+s, 0);
                            }
                        }
                    }
                });
                break;
            default:
                log.warn("未知的Canal数据库类型：{}", table);
        }
    }

    /**
     * 发送SKU库存预警消息
     * @param skuStock 商品SKU库存信息
     * @param type 预警类型 1：有库存 2：无库存
     */
    public void sendSkuStockWarningMessage(SkuStock skuStock,int type){
        //发送消息
        SkuStockWarningContent stockWarningContent=new SkuStockWarningContent();
        stockWarningContent.setType(type);
        stockWarningContent.setTime(DateUtil.date());
        stockWarningContent.setProductSkuCode(skuStock.getProductSkuCode());
        stockWarningContent.setPickUpStock(skuStock.getPickUpStockTotal());
        stockWarningContent.setDropShippingStock(skuStock.getDropShippingStockTotal());
        String jsonStr = JSONUtil.toJsonStr(stockWarningContent);
        WarningMessage warningMessage=new WarningMessage();
        warningMessage.setTenantType(TenantType.Distributor.name());
        warningMessage.setBusinessType(1);
        warningMessage.setContent(jsonStr);
        warningMessage.setIsRead(0);
        warningMessageMapper.insert(warningMessage);
    }
}
