package com.zsmall.product.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.constant.GlobalConstants;
import com.hengjian.common.redis.utils.RedisUtils;
import com.zsmall.product.biz.service.StockQueryService;
import com.zsmall.product.biz.service.StockStatusInitService;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 库存状态初始化服务实现
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StockStatusInitServiceImpl implements StockStatusInitService {

    private final IProductSkuStockService productSkuStockService;
    private final StockQueryService stockQueryService;

    /**
     * 初始化进度缓存Key
     */
    private static final String INIT_PROGRESS_KEY = GlobalConstants.GLOBAL_REDIS_KEY + "stock:init:progress";

    /**
     * 默认批次大小
     */
    private static final int DEFAULT_BATCH_SIZE = 1000;

    /**
     * 初始化所有商品的库存状态
     */
    @Override
    public Long initAllStockStatus() {
        return initStockStatusInBatches(DEFAULT_BATCH_SIZE);
    }

    /**
     * 初始化指定商品的库存状态
     */
    @Override
    public Boolean initStockStatus(String productSkuCode, String warehouseSystemCode) {
        if (StrUtil.isBlank(productSkuCode) || StrUtil.isBlank(warehouseSystemCode)) {
            log.warn("初始化库存状态参数为空: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode);
            return false;
        }

        try {
            // 查询当前库存状态
            Integer stockQuantity = stockQueryService.getDropShippingStockQuantity(productSkuCode, warehouseSystemCode);
            boolean hasStock = stockQuantity != null && stockQuantity > 0;

            // 设置缓存状态


            log.debug("初始化单个库存状态成功: productSkuCode={}, warehouseSystemCode={}, hasStock={}",
                     productSkuCode, warehouseSystemCode, hasStock);
            return true;

        } catch (Exception e) {
            log.error("初始化单个库存状态异常: productSkuCode={}, warehouseSystemCode={}",
                     productSkuCode, warehouseSystemCode, e);
            return false;
        }
    }

    /**
     * 分批初始化库存状态
     */
    @Override
    public Long initStockStatusInBatches() {



        return totalProcessed.get();
    }

    /**
     * 处理单个批次
     */
    private long processBatch(List<ProductSkuStock> records) {
        Map<String, Boolean> stockStatusMap = new HashMap<>();
        long processedCount = 0;

        for (ProductSkuStock stock : records) {
            try {
                String productSkuCode = stock.getProductSkuCode();
                String warehouseSystemCode = stock.getWarehouseSystemCode();

                if (StrUtil.isBlank(productSkuCode) || StrUtil.isBlank(warehouseSystemCode)) {
                    continue;
                }

                // 查询库存状态
                Integer stockQuantity = stockQueryService.getDropShippingStockQuantity(productSkuCode, warehouseSystemCode);
                boolean hasStock = stockQuantity != null && stockQuantity > 0;

                // 构建缓存Key
                String stockKey = productSkuCode + ":" + warehouseSystemCode;
                stockStatusMap.put(stockKey, hasStock);

                processedCount++;

            } catch (Exception e) {
                log.error("处理库存记录异常: {}", stock, e);
            }
        }



        return processedCount;
    }

    /**
     * 重新初始化所有库存状态
     */
    @Override
    public Long reinitAllStockStatus() {
        log.info("开始重新初始化所有库存状态");

        try {
            // 清空现有缓存

            log.info("已清空现有库存状态缓存");

            // 重新初始化
            return initAllStockStatus();

        } catch (Exception e) {
            log.error("重新初始化库存状态异常", e);
            throw new RuntimeException("重新初始化库存状态异常: " + e.getMessage(), e);
        }
    }

    /**
     * 获取初始化进度
     */
    @Override
    public InitProgressInfo getInitProgress() {
        try {
            InitProgressInfo progressInfo = RedisUtils.getCacheObject(INIT_PROGRESS_KEY);
            if (progressInfo == null) {
                progressInfo = new InitProgressInfo();
                progressInfo.setStatus("NOT_STARTED");
                progressInfo.setMessage("未开始初始化");
                progressInfo.setTotalCount(0L);
                progressInfo.setProcessedCount(0L);
                progressInfo.setProgressPercentage(0.0);
            }
            return progressInfo;
        } catch (Exception e) {
            log.error("获取初始化进度异常", e);
            InitProgressInfo errorInfo = new InitProgressInfo();
            errorInfo.setStatus("ERROR");
            errorInfo.setMessage("获取进度失败: " + e.getMessage());
            return errorInfo;
        }
    }

    /**
     * 更新初始化进度
     */
    private void updateInitProgress(Long totalCount, Long processedCount, String status, String message) {
        try {
            InitProgressInfo progressInfo = new InitProgressInfo();
            progressInfo.setTotalCount(totalCount);
            progressInfo.setProcessedCount(processedCount);
            progressInfo.setStatus(status);
            progressInfo.setMessage(message);

            if (totalCount > 0) {
                double percentage = (double) processedCount / totalCount * 100;
                progressInfo.setProgressPercentage(percentage);
            } else {
                progressInfo.setProgressPercentage(0.0);
            }

            RedisUtils.setCacheObject(INIT_PROGRESS_KEY, progressInfo, Duration.ofHours(24));
        } catch (Exception e) {
            log.error("更新初始化进度异常", e);
        }
    }
}
