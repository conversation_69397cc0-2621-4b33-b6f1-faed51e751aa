package com.zsmall.product.biz.service;

/**
 * 库存状态初始化服务接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface StockStatusInitService {

    /**
     * 初始化所有商品的库存状态
     *
     * @return 初始化的记录数量
     */
    Long initAllStockStatus();

    /**
     * 初始化指定商品的库存状态
     *
     * @param productSkuCode SKU编码
     * @param warehouseSystemCode 仓库系统编码
     * @return 是否初始化成功
     */
    Boolean initStockStatus(String productSkuCode, String warehouseSystemCode);

    /**
     * 分批初始化库存状态
     *
     * @param batchSize 批次大小
     * @return 初始化的记录数量
     */
    Long initStockStatusInBatches();

    /**
     * 重新初始化所有库存状态（清空后重新初始化）
     *
     * @return 初始化的记录数量
     */
    Long reinitAllStockStatus();

    /**
     * 获取初始化进度
     *
     * @return 初始化进度信息
     */
    InitProgressInfo getInitProgress();

    /**
     * 初始化进度信息
     */
    class InitProgressInfo {
        private Long totalCount;
        private Long processedCount;
        private Double progressPercentage;
        private String status;
        private String message;

        // Getters and Setters
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }

        public Long getProcessedCount() { return processedCount; }
        public void setProcessedCount(Long processedCount) { this.processedCount = processedCount; }

        public Double getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(Double progressPercentage) { this.progressPercentage = progressPercentage; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

}
